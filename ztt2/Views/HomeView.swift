//
//  HomeView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 首页视图 - 家庭成员管理
 */
struct HomeView: View {

    // MARK: - Properties
    let onMemberSelected: (String) -> Void

    @State private var pageAppeared = false
    @State private var logoRotation: Double = 0
    @State private var isDeleteMode = false
    @State private var showAddMemberForm = false
    @State private var showFamilyOperationOptions = false
    @State private var showFamilyOperationForm = false
    @State private var showFamilyTotalScore = false
    @State private var showLotteryConfigDialog = false
    @State private var lotteryConfigPressed = false
    @State private var familyOperationType: FamilyOperationType = .add
    @State private var selectedDateRange: DateRangeType = .thisMonth

    // 示例数据 - 实际项目中应该从ViewModel获取
    @State private var sampleMembers: [FamilyMemberGridView.FamilyMember] = [
        FamilyMemberGridView.FamilyMember(id: "1", name: "爸爸", role: "father", currentPoints: 0),
        FamilyMemberGridView.FamilyMember(id: "2", name: "妈妈", role: "mother", currentPoints: 0),
        FamilyMemberGridView.FamilyMember(id: "3", name: "多多", role: "son", currentPoints: 10),
        FamilyMemberGridView.FamilyMember(id: "4", name: "朵朵", role: "daughter", currentPoints: 5)
    ]

    var totalScore: Int {
        sampleMembers.reduce(0) { $0 + $1.currentPoints }
    }

    var body: some View {
        ZStack {
            // 美化背景渐变
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color(hex: "#fcfff4"), location: 0.0),
                    .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                    .init(color: Color.white, location: 0.7),
                    .init(color: Color(hex: "#fafffe"), location: 1.0)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea(.all)

            // 装饰性背景元素
            VStack {
                HStack {
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.03))
                        .frame(width: 120, height: 120)
                        .offset(x: 40, y: -20)
                }
                Spacer()
                HStack {
                    Circle()
                        .fill(Color(hex: "#FFE49E").opacity(0.04))
                        .frame(width: 80, height: 80)
                        .offset(x: -30, y: 50)
                    Spacer()
                }
            }

            VStack(spacing: 0) {
                // 顶部区域 - Logo和配置按钮
                HStack {
                    // Logo with enhanced visual effects
                    ZStack {
                        // 背景渐变圆形
                        Circle()
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#FFE49E").opacity(0.3),
                                        Color(hex: "#B5E36B").opacity(0.2)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 110, height: 110)
                            .shadow(color: Color(hex: "#B5E36B").opacity(0.3), radius: 15, x: 0, y: 5)

                        // Logo图片
                        Image("logo")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 90, height: 90)
                            .rotationEffect(.degrees(logoRotation))
                            .onTapGesture {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    logoRotation += 360
                                }
                            }
                    }
                    .offset(y: -20)

                    Spacer()

                    // 抽奖道具配置按钮
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            lotteryConfigPressed = true
                        }

                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            lotteryConfigPressed = false
                            handleLotteryConfig()
                        }
                    }) {
                        ZStack {
                            // 美化背景容器
                            RoundedRectangle(cornerRadius: 16)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color(hex: "#f8ffe5"),
                                            Color(hex: "#edf6d9")
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 120, height: 44)
                                .shadow(color: Color(hex: "#a9d051").opacity(0.25), radius: lotteryConfigPressed ? 10 : 6, x: 0, y: lotteryConfigPressed ? 4 : 2)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
                                )

                            // 装饰圆点
                            HStack {
                                Spacer()
                                VStack {
                                    Circle()
                                        .fill(Color(hex: "#a9d051").opacity(0.15))
                                        .frame(width: 20, height: 20)
                                        .offset(x: 8, y: -8)
                                    Spacer()
                                }
                            }

                            // 内容
                            HStack(spacing: 6) {
                                // 图标
                                Image("shezhi")
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: 20, height: 20)
                                    .foregroundColor(Color(hex: "#a9d051"))

                                // 文字
                                Text("抽奖配置")
                                    .font(.system(size: 14, weight: .semibold))
                                    .foregroundColor(Color(hex: "#a9d051"))
                                    .lineLimit(1)
                                    .minimumScaleFactor(0.8)
                            }

                            // 按压闪烁效果
                            if lotteryConfigPressed {
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(Color.white.opacity(0.4))
                                    .frame(width: 120, height: 44)
                                    .transition(.opacity)
                            }
                        }
                        .frame(width: 120, height: 44)
                        .scaleEffect(lotteryConfigPressed ? 0.95 : 1.0)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .offset(y: -20)
                }
                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                .padding(.top, 5)
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : -50)
                .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.1), value: pageAppeared)

                Spacer()
                    .frame(height: 5)

                // 操作按钮区域
                ActionButtonsView(
                    totalScore: totalScore,
                    dateRangeText: "本月",
                    onAddMemberTapped: {
                        handleAddMember()
                    },
                    onFamilyOperationTapped: {
                        handleFamilyOperation()
                    },
                    onTotalScoreTapped: {
                        handleTotalScoreTapped()
                    }
                )
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : 30)
                .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: pageAppeared)

                Spacer()
                    .frame(height: 20)

                // 家庭成员网格视图
                FamilyMemberGridView(
                    members: sampleMembers,
                    isDeleteMode: isDeleteMode,
                    hasFamilies: true, // 示例中假设有家庭
                    onMemberTapped: { member in
                        handleMemberTapped(member)
                    },
                    onEnterDeleteMode: {
                        enterDeleteMode()
                    },
                    onExitDeleteMode: {
                        exitDeleteMode()
                    },
                    onDeleteRequested: { member in
                        requestDeleteMember(member)
                    },
                    onCreateFamilyTapped: {
                        handleCreateFamily()
                    },
                    onRefresh: {
                        await refreshData()
                    }
                )
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : 30)
                .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.7), value: pageAppeared)
            }
        }
        .onTapGesture {
            // 点击空白区域退出删除模式
            if isDeleteMode {
                exitDeleteMode()
            }
        }
        .onAppear {
            // Logo入场动画
            withAnimation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2)) {
                logoRotation = 0
            }

            // 页面入场动画
            withAnimation {
                pageAppeared = true
            }
        }
        .overlay(
            // 添加成员表单弹窗
            AddMemberFormView(
                isPresented: $showAddMemberForm,
                onSubmit: { memberData in
                    handleAddMemberSubmit(memberData)
                },
                onCancel: {
                    showAddMemberForm = false
                }
            )
        )
        .overlay(
            // 全家操作选项弹窗
            FamilyOperationOptionsView(
                isPresented: $showFamilyOperationOptions,
                onAddPoints: {
                    familyOperationType = .add
                    showFamilyOperationForm = true
                },
                onDeductPoints: {
                    familyOperationType = .deduct
                    showFamilyOperationForm = true
                }
            )
        )
        .overlay(
            // 全家操作表单弹窗
            FamilyOperationFormView(
                isPresented: $showFamilyOperationForm,
                operationType: familyOperationType,
                onSubmit: { name, value in
                    handleFamilyOperationSubmit(name: name, value: value, type: familyOperationType)
                },
                onCancel: {
                    showFamilyOperationForm = false
                }
            )
        )
        .overlay(
            // 全家总积分弹窗
            FamilyTotalScoreView(
                isPresented: $showFamilyTotalScore,
                selectedDateRange: $selectedDateRange,
                totalScore: calculateTotalScore(),
                dateRangeText: selectedDateRange.displayText
            )
        )
        .alert("抽奖道具配置", isPresented: $showLotteryConfigDialog) {
            Button("确定") {
                // TODO: 实现抽奖道具配置功能
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("抽奖道具配置功能开发中...")
        }
    }

    // MARK: - Action Handlers

    /**
     * 处理添加成员按钮点击
     */
    private func handleAddMember() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showAddMemberForm = true
        }
    }

    /**
     * 处理全家操作按钮点击
     */
    private func handleFamilyOperation() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showFamilyOperationOptions = true
        }
    }

    /**
     * 处理抽奖道具配置按钮点击
     */
    private func handleLotteryConfig() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showLotteryConfigDialog = true
        }
    }

    /**
     * 处理全家总分按钮点击
     */
    private func handleTotalScoreTapped() {
        print("显示全家总积分弹窗")
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            showFamilyTotalScore = true
        }
    }

    /**
     * 处理添加成员表单提交
     */
    private func handleAddMemberSubmit(_ memberData: MemberFormData) {
        print("添加成员: \(memberData.name), 初始积分: \(memberData.initialPointsValue), 角色: \(memberData.role)")

        // TODO: 实现添加成员到数据库的逻辑
        // 这里可以调用DataManager来添加成员

        showAddMemberForm = false
    }

    /**
     * 处理全家操作表单提交
     */
    private func handleFamilyOperationSubmit(name: String, value: Int, type: FamilyOperationType) {
        let operationText = type == .add ? "全家加分" : "全家扣分"
        print("\(operationText): \(name), 分值: \(value)")

        // TODO: 实现全家操作的逻辑
        // 这里可以调用DataManager来为所有成员加分或扣分

        showFamilyOperationForm = false
    }

    /**
     * 计算全家总积分
     */
    private func calculateTotalScore() -> Int {
        // TODO: 实现计算全家总积分的逻辑
        // 这里应该从DataManager获取所有成员的积分总和
        return 1250 // 临时返回值
    }

    /**
     * 处理家庭成员卡片点击
     */
    private func handleMemberTapped(_ member: FamilyMemberGridView.FamilyMember) {
        print("点击了家庭成员: \(member.name)")
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            // 添加点击反馈动画
        }

        // 调用导航回调，传递成员ID
        onMemberSelected(member.id)
    }

    /**
     * 进入删除模式
     */
    private func enterDeleteMode() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            isDeleteMode = true
        }
    }

    /**
     * 退出删除模式
     */
    private func exitDeleteMode() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            isDeleteMode = false
        }
    }

    /**
     * 请求删除成员
     */
    private func requestDeleteMember(_ member: FamilyMemberGridView.FamilyMember) {
        print("请求删除成员: \(member.name)")
        // TODO: 显示删除确认对话框
    }

    /**
     * 处理创建家庭按钮点击
     */
    private func handleCreateFamily() {
        print("创建家庭按钮点击")
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            // TODO: 实现创建家庭功能
        }
    }

    /**
     * 刷新数据
     */
    private func refreshData() async {
        print("下拉刷新数据")
        // TODO: 实现数据刷新
    }
}

// MARK: - Preview
#Preview {
    HomeView { memberId in
        print("选中成员: \(memberId)")
    }
}
