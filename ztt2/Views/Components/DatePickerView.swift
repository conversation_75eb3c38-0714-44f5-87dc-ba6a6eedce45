//
//  DatePickerView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 日期选择器弹窗组件
 * 用于选择出生日期
 */
struct DatePickerView: View {
    
    @Binding var selectedDate: Date
    @Binding var isPresented: Bool
    
    var body: some View {
        ZStack {
            // 半透明背景
            if isPresented {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .onTapGesture {
                        isPresented = false
                    }

                VStack {
                    Spacer()

                    VStack(spacing: 0) {
                        // 标题栏
                        HStack {
                            Button("取消") {
                                isPresented = false
                            }
                            .foregroundColor(.gray)

                            Spacer()

                            Text("选择出生日期")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)

                            Spacer()

                            Button("确定") {
                                isPresented = false
                            }
                            .foregroundColor(Color(hex: "#a9d051"))
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                        .background(Color.white)

                        // 日期选择器
                        DatePicker(
                            "",
                            selection: $selectedDate,
                            in: ...Date(),
                            displayedComponents: .date
                        )
                        .datePickerStyle(WheelDatePickerStyle())
                        .labelsHidden()
                        .background(Color.white)
                    }
                    .cornerRadius(12)
                    .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
                }
                .padding(.horizontal, 20)
                .transition(.move(edge: .bottom))
            }
        }
        .animation(.easeInOut(duration: 0.3), value: isPresented)
    }
}

// MARK: - Preview
#Preview {
    DatePickerView(
        selectedDate: .constant(Date()),
        isPresented: .constant(true)
    )
}
